"""
FastAPI应用程序入口
该模块创建并配置FastAPI应用程序
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from loguru import logger
import os

from api.routes import router
# from mi_otel_python import exporter
# # 根据需要选择对应框架的opentelemetry依赖：https://xiaomi.f.mioffice.cn/docx/doxk4JtZZuZVfUrKSdb8PuPsGme?from=from_copylink 
# from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
# from opentelemetry.instrumentation.requests import RequestsInstrumentor
# from opentelemetry.instrumentation.redis import RedisInstrumentor
# from opentelemetry.instrumentation.sqlalchemy import SQLAlchemyInstrumentor

# 创建FastAPI应用
app = FastAPI(
    title="ipd问答助手",
    description="ipd知识库问答助手",
    version="0.2"
)


# # app初始化之后，初始化opentelemetry
# access_key = os.environ.get("access_key","")
# access_secret = os.environ.get("access_secret","")
# exporter.init(access_key, access_secret, trace_debug=True)    # 测试时需要传入 traceDebug=True，否则使用默认值
# RequestsInstrumentor().instrument()
# SQLAlchemyInstrumentor().instrument()
# RedisInstrumentor().instrument()
# FastAPIInstrumentor().instrument_app(app)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(router, prefix="/api/v1")

@app.get("/")
async def root():
    """根路径，返回API信息"""
    return {
        "message": "ipd问答助手",
        "version": "0.1",
        "status": "运行中"
    }

# 启动事件
@app.on_event("startup")
async def startup_event():
    logger.info("应用程序启动")

# 关闭事件
@app.on_event("shutdown")
async def shutdown_event():
    logger.info("应用程序关闭")

# 如果直接运行此文件，则启动应用
if __name__ == "__main__":
    import uvicorn
    uvicorn.run("app:app", host="0.0.0.0", port=8080, reload=True)