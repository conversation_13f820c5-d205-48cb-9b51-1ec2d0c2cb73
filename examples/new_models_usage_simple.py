"""
新模型使用简化示例
展示如何使用新的235B思考模型和非思考模型
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 添加环境变量加载代码
from dotenv import load_dotenv

# 获取环境变量中的环境类型，默认为local
env_type = os.environ.get("ENVIRONMENT", "local")
env_file = f".env.{env_type}"

# 尝试加载对应环境的配置文件
env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), env_file)
if os.path.exists(env_path):
    load_dotenv(env_path)
    print(f"已加载{env_type}环境配置: {env_path}")
else:
    # 如果特定环境配置不存在，尝试加载默认.env文件
    default_env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env')
    if os.path.exists(default_env_path):
        load_dotenv(default_env_path)
        print(f"已加载默认环境配置: {default_env_path}")
    else:
        print(f"警告: 环境配置文件不存在: {env_path} 或 {default_env_path}")

def example_model_selection():
    """模型选择示例"""
    print("=== 模型选择示例 ===")
    
    from core.llm_provider import get_llm_provider
    
    # 示例1: 使用235B模型，启用思考
    print("1. 使用235B模型，启用思考:")
    provider_thinking = get_llm_provider("qwen3_235b_2507", "req_001", enable_thinking=True)
    print(f"   选择的Provider: {provider_thinking.__class__.__name__}")
    print(f"   实际模型ID: {provider_thinking.model_id}")
    print(f"   模型名称: {provider_thinking.config['model']}")
    
    # 示例2: 使用235B模型，禁用思考
    print("\n2. 使用235B模型，禁用思考:")
    provider_instruct = get_llm_provider("qwen3_235b_2507", "req_002", enable_thinking=False)
    print(f"   选择的Provider: {provider_instruct.__class__.__name__}")
    print(f"   实际模型ID: {provider_instruct.model_id}")
    print(f"   模型名称: {provider_instruct.config['model']}")
    
    # 示例3: 使用混合思考模型
    print("\n3. 使用混合思考模型:")
    provider_hybrid = get_llm_provider("qwen3_32b", "req_003")
    print(f"   选择的Provider: {provider_hybrid.__class__.__name__}")
    print(f"   实际模型ID: {provider_hybrid.model_id}")
    print(f"   模型名称: {provider_hybrid.config['model']}")

def example_model_config():
    """模型配置示例"""
    print("\n=== 模型配置示例 ===")
    
    from config.model_config import MODEL_CONFIG, get_model_type
    
    models = ["qwen3_32b", "qwen3_8b", "qwen3_235b_thinking", "qwen3_235b_instruct"]
    
    for model_id in models:
        config = MODEL_CONFIG[model_id]
        model_type = get_model_type(model_id)
        print(f"\n模型ID: {model_id}")
        print(f"  模型类型: {model_type}")
        print(f"  服务类型: {config['service_type']}")
        print(f"  模型名称: {config['model']}")
        print(f"  描述: {config['description']}")

def example_api_usage():
    """API使用示例"""
    print("\n=== API使用示例 ===")
    
    print("1. LLM问答API - 使用235B思考模型:")
    print("POST /api/v1/llm-qa")
    print("""{
    "query": "什么是人工智能？",
    "user_id": "user_123",
    "model_id": "qwen3_235b_2507",
    "msg_id": "msg_001",
    "conversation_id": "conv_001",
    "history": [],
    "stream": true,
    "enable_thinking": true
}""")
    
    print("\n2. LLM问答API - 使用235B非思考模型:")
    print("POST /api/v1/llm-qa")
    print("""{
    "query": "什么是人工智能？",
    "user_id": "user_123",
    "model_id": "qwen3_235b_2507",
    "msg_id": "msg_002",
    "conversation_id": "conv_002",
    "history": [],
    "stream": true,
    "enable_thinking": false
}""")
    
    print("\n3. 数据问答API - 使用235B思考模型:")
    print("POST /api/v1/data-qa")
    print("""{
    "query": "库存周转率如何计算？",
    "user_id": "user_123",
    "model_id": "qwen3_235b_2507",
    "msg_id": "msg_003",
    "conversation_id": "conv_003",
    "history": [],
    "stream": true,
    "enable_thinking": true,
    "top_k": 20,
    "mode": "strict"
}""")

def example_curl_commands():
    """cURL命令示例"""
    print("\n=== cURL命令示例 ===")
    
    print("1. 思考模型直接调用 (Qwen3-235B-A22B-Thinking-2507):")
    print("""curl -X 'POST' \\
  'http://s-20250807205639-4a0dp.ak-cloudml.xiaomi.srv/v1/chat/completions' \\
  -H 'accept: application/json' \\
  -H 'Content-Type: application/json' \\
  -d '{
  "model": "Qwen3-235B-A22B-Thinking-2507",
  "stream": true,
  "temperature": 0.5,
  "top_p": 0.5,
  "max_tokens": 2048,
  "messages": [
    {
      "content": "你是一个专业的问答助手",
      "role": "system"
    },
    {
      "content": "什么是人工智能？",
      "role": "user"
    }
  ]
}'""")
    
    print("\n2. 非思考模型直接调用 (Qwen3-235B-A22B-Instruct-2507):")
    print("""curl -X 'POST' \\
  'http://s-20250807205639-4a0dp.ak-cloudml.xiaomi.srv/v1/chat/completions' \\
  -H 'accept: application/json' \\
  -H 'Content-Type: application/json' \\
  -d '{
  "model": "Qwen3-235B-A22B-Instruct-2507",
  "stream": true,
  "temperature": 0.5,
  "top_p": 0.5,
  "max_tokens": 2048,
  "messages": [
    {
      "content": "你是一个专业的问答助手",
      "role": "system"
    },
    {
      "content": "什么是人工智能？",
      "role": "user"
    }
  ]
}'""")

def example_output_format():
    """输出格式示例"""
    print("\n=== 输出格式示例 ===")
    
    print("1. 思考模型输出 (包含reasoning和content):")
    print("""data: {"type": "reasoning", "content": "用户询问人工智能的定义，我需要给出准确和全面的解释...", "role": "assistant", "finish_reason": ""}
data: {"type": "content", "content": "人工智能（AI）是指", "role": "assistant", "finish_reason": ""}
data: {"type": "content", "content": "计算机系统模拟人类智能的技术...", "role": "assistant", "finish_reason": "stop"}""")
    
    print("\n2. 非思考模型输出 (只有content):")
    print("""data: {"type": "content", "content": "人工智能（AI）是指", "role": "assistant", "finish_reason": ""}
data: {"type": "content", "content": "计算机系统模拟人类智能的技术...", "role": "assistant", "finish_reason": "stop"}""")
    
    print("\n3. 混合思考模型输出 (根据enable_thinking参数):")
    print("   enable_thinking=true: 包含reasoning和content")
    print("   enable_thinking=false: 只有content (用户消息会自动添加/no_think)")

def example_environment_setup():
    """环境配置示例"""
    print("\n=== 环境配置示例 ===")
    
    print("1. 环境变量设置:")
    print("export QWEN3_235B_BASE_URL='http://s-20250807205639-4a0dp.ak-cloudml.xiaomi.srv/v1'")
    print("export AUTH_TOKEN_QWEN3_235B='your_api_token_here'")
    
    print("\n2. 模型映射关系:")
    print("用户传参 -> 实际调用模型")
    print("qwen3_235b_2507 + enable_thinking=true  -> Qwen3-235B-A22B-Thinking-2507")
    print("qwen3_235b_2507 + enable_thinking=false -> Qwen3-235B-A22B-Instruct-2507")
    print("qwen3_32b + enable_thinking=true         -> Qwen3-32B (with enable_reasoning=true)")
    print("qwen3_32b + enable_thinking=false        -> Qwen3-32B (with /no_think suffix)")

def main():
    """运行所有示例"""
    print("新模型使用示例")
    print("=" * 60)
    
    try:
        example_model_selection()
        example_model_config()
        example_api_usage()
        example_curl_commands()
        example_output_format()
        example_environment_setup()
        
        print("\n" + "=" * 60)
        print("🎉 示例完成！")
        
        print("\n📋 总结:")
        print("1. 用户使用统一的模型ID: qwen3_235b_2507")
        print("2. 系统根据enable_thinking参数自动选择:")
        print("   - True:  调用思考模型 (Qwen3-235B-A22B-Thinking-2507)")
        print("   - False: 调用非思考模型 (Qwen3-235B-A22B-Instruct-2507)")
        print("3. 输出格式保持一致，便于前端处理")
        print("4. 支持所有现有的API接口 (llm-qa, data-qa, rag-qa, all-qa等)")
        
    except Exception as e:
        print(f"❌ 示例运行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
