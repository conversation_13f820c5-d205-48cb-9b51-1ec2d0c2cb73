"""
日志配置文件
"""

from loguru import logger
import sys
import os

def configure_logging():
    """统一日志配置"""
    logger.remove()
    
    # 定义格式化函数
    def format_record(record):
        request_id = record["extra"].get("request_id")
        request_id_part = f"request_id={request_id} | " if request_id else ""
        # 安全处理消息内容，避免格式化错误
        message = record["message"]
        # 如果消息中包含花括号，将其转义
        if isinstance(message, str) and ("{" in message or "}" in message):
            message = message.replace("{", "{{")
            message = message.replace("}", "}}")
        return (
            "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
            "<level>{level: <4}</level> | "
            "<magenta>{extra[request_id_part]}</magenta>"
            "<cyan>{name}</cyan>:"
            "<cyan>{function}</cyan>:"
            "<yellow>{line}</yellow> | "
            "<level>{message}</level>\n"
        ).format(
            time=record["time"].replace(tzinfo=None),
            level=record["level"].name,
            extra={"request_id_part": request_id_part},
            name=record["name"],
            function=record["function"].replace('<', '\<').replace('>', '\>'),
            line=record["line"],
            message=message
        )
    
    # 添加标准输出处理器
    logger.add(
        sys.stdout,
        format=format_record,
        level="DEBUG",
        enqueue=True,
        backtrace=True,
        diagnose=True
    )
    
    # 添加文件处理器
    workdir = os.environ.get("log_dir", "/home/<USER>/log")
    if not os.path.exists(workdir):
        os.makedirs(workdir)
        
    logger.add(
        f"{workdir}/api.log",
        format=format_record,
        rotation="50 MB",
        retention="14 days",
        enqueue=True,
        backtrace=True,
        diagnose=True,
        level="DEBUG"
    )