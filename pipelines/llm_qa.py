from typing import List, Dict, Any, AsyncGenerator, Optional
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.llm_provider import get_llm_provider
from prompts.llm_qa_prompt import system_prompt, user_query
import json
from loguru import logger
from config.logging_config import configure_logging
configure_logging()

class LLMQA:
    """LLM问答基础类，支持流式和非流式输出"""
    
    def __init__(self, model_id: str, request_id: str = None):
        """
        初始化问答实例

        Args:
            model_id: 模型ID (e.g. "gpt_4o", "qwen3_32b", "qwen3_235b_2507")
            request_id: 可选请求ID
        """
        self.model_id = model_id
        self.request_id = request_id
        self.logger = logger.bind(request_id=request_id)

    def _get_provider(self, enable_thinking: bool = True):
        """根据enable_thinking参数获取合适的provider"""
        return get_llm_provider(self.model_id, self.request_id, enable_thinking)
    
    def _build_messages(self, query: str, history: List[Dict]) -> List[Dict]:
        """构建OpenAI格式的消息列表（适配新history格式）"""
        self.logger.info(f"构建消息列表: {query}")
        messages = [{"role": "system", "content": system_prompt}]
        # print(f"history: {history}")
        # 适配新格式history
        for item in history[::-1]:
            # 安全检查：确保item是字典类型且包含必要的键
            if isinstance(item, dict) and "query" in item and "content" in item:
                messages.append({"role": "user", "content": item["query"]})
                messages.append({"role": "assistant", "content": item["content"]})
            else:
                # 记录无效的历史记录项
                self.logger.warning(f"跳过无效的历史记录项: {item}")
        # 添加当前查询
        formatted_query = user_query.format(query=query)
        messages.append({"role": "user", "content": formatted_query})
        return messages
    
    async def generate(
        self,
        query: str,
        user_id: str,
        history: List[Dict],
        timeout: Optional[float] = None,
        conversation_id: Optional[str] = None,
        enable_thinking: bool = False,
        **kwargs
    ) -> Dict[str, Any]:
        """
        非流式问答生成
        """
        messages = self._build_messages(query, history)
        provider = self._get_provider(enable_thinking)
        return await provider.generate(
            messages=messages,
            timeout=timeout,
            conversation_id=conversation_id,
            enable_thinking=enable_thinking,
            **kwargs
        )
    
    async def generate_stream(
        self,
        query: str,
        user_id: str,
        history: List[Dict],
        timeout: Optional[float] = None,
        conversation_id: Optional[str] = None,
        enable_thinking: bool = True,
        **kwargs
    ) -> AsyncGenerator[Dict[str, Any], None]:
        # 模型服务的流式调用
        messages = self._build_messages(query, history)
        self.logger.info(f"对话消息列表: {json.dumps(messages,ensure_ascii=False)[:500]}...")
        # print(f"message: {messages}")
        provider = self._get_provider(enable_thinking)
        async for chunk in provider.generate_stream(
            messages=messages,
            timeout=timeout,
            conversation_id=conversation_id,
            enable_thinking=enable_thinking,
            **kwargs
        ):
            yield chunk
                
