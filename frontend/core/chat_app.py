#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ChatApp核心类模块
负责管理对话历史和基础功能
"""

import os
import sys
from typing import Dict, Any, List
from loguru import logger

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# 导入配置
try:
    from frontend.frontend_config import API_BASE_URL
    logger.info("成功导入前端配置文件")
except ImportError:
    logger.info("前端配置文件不存在，使用默认值")
    API_BASE_URL = "http://dms.ai.xiaomi.com/api/v1"


class ChatApp:
    """聊天应用核心类"""
    
    def __init__(self, api_url: str = API_BASE_URL):
        """初始化ChatApp
        
        Args:
            api_url: API基础URL
        """
        self.api_url = api_url
        # 为每个API类型维护独立的对话历史
        self.llm_conversation_history = []
        self.rag_conversation_history = []
        self.dataqa_conversation_history = []
        self.car_conversation_history = []  # 汽车知识库对话历史
        self.all_conversation_history = []  # all问答对话历史
        self.first_token_time_component = None  # 首token时间显示组件
        logger.info(f"ChatApp初始化，API地址: {api_url}")
    
    def set_first_token_time_component(self, component):
        """设置首token时间显示组件
        
        Args:
            component: Gradio组件
        """
        self.first_token_time_component = component
    
    def update_first_token_time(self, time_str: str):
        """更新首token响应时间显示
        
        Args:
            time_str: 时间字符串
        """
        if self.first_token_time_component:
            # 这里需要通过Gradio的更新机制来更新组件
            # 由于Gradio的限制，我们将在处理器中直接返回更新值
            pass

    def clear_history(self, api_type: str = "all") -> str:
        """清空历史对话
        
        Args:
            api_type: API类型，可选值：all, llm, rag, dataqa, car, allqa, search
            
        Returns:
            空字符串
        """
        if api_type == "all":
            self.llm_conversation_history = []
            self.rag_conversation_history = []
            self.dataqa_conversation_history = []
            self.car_conversation_history = []
            self.all_conversation_history = []
        elif api_type == "llm":
            self.llm_conversation_history = []
        elif api_type == "rag":
            self.rag_conversation_history = []
        elif api_type == "dataqa":
            self.dataqa_conversation_history = []
        elif api_type == "car":
            self.car_conversation_history = []
        elif api_type == "allqa":
            self.all_conversation_history = []
        elif api_type == "search":
            # 检索没有历史记录，但为了统一接口
            pass
        
        logger.info(f"清空{api_type}对话历史")
        return ""
    
    def get_conversation_history(self, api_type: str) -> List[Dict[str, Any]]:
        """获取指定类型的对话历史
        
        Args:
            api_type: API类型
            
        Returns:
            对话历史列表
        """
        if api_type == "llm":
            return self.llm_conversation_history
        elif api_type == "rag":
            return self.rag_conversation_history
        elif api_type == "dataqa":
            return self.dataqa_conversation_history
        elif api_type == "car":
            return self.car_conversation_history
        elif api_type == "allqa":
            return self.all_conversation_history
        else:
            return []
    
    def add_to_history(self, api_type: str, query: str, content: str):
        """添加对话到历史记录
        
        Args:
            api_type: API类型
            query: 用户查询
            content: 助手回复
        """
        conversation_item = {"query": query, "content": content}
        
        if api_type == "llm":
            self.llm_conversation_history.append(conversation_item)
        elif api_type == "rag":
            self.rag_conversation_history.append(conversation_item)
        elif api_type == "dataqa":
            self.dataqa_conversation_history.append(conversation_item)
        elif api_type == "car":
            self.car_conversation_history.append(conversation_item)
        elif api_type == "allqa":
            self.all_conversation_history.append(conversation_item)
        
        logger.debug(f"添加{api_type}对话到历史: query={query[:50]}..., content={content[:50]}...")