#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于Gradio的前端应用 - 重构版本
实现LLM问答、RAG问答、DATAQA问答的Web界面
"""

import gradio as gr
import os
import sys
from loguru import logger

# 设置日志
log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'log')
os.makedirs(log_dir, exist_ok=True)
logger.remove()
log_path = os.path.join(log_dir, 'frontend.log')
logger.add(log_path, rotation="20 MB", retention="10 days", encoding="utf-8", enqueue=True, backtrace=True, diagnose=True, level="DEBUG")
logger.add(sys.stdout, level="INFO")
logger.info(f"日志初始化完成，日志路径: {log_path}")

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入配置
try:
    from frontend.frontend_config import (
        API_BASE_URL, DEFAULT_MODELS, DEFAULT_TOP_K, DEFAULT_MODEL,
        THEME, TITLE, REQUEST_TIMEOUT,
        GRADIO_HOST, GRADIO_PORT, GRADIO_SHARE, GRADIO_DEBUG
    )
    logger.info("成功导入前端配置文件")
except ImportError:
    logger.info("前端配置文件不存在，使用默认值")
    API_BASE_URL = "http://dms.ai.xiaomi.com/api/v1"
    DEFAULT_MODELS = ["qwen3_32b", "gpt-4", "claude-3"]
    DEFAULT_TOP_K = 3
    DEFAULT_MODEL = "qwen3_32b"
    THEME = "soft"
    TITLE = "🤖 问题库AI"
    REQUEST_TIMEOUT = 600.0
    GRADIO_HOST = "0.0.0.0"
    GRADIO_PORT = 7860
    GRADIO_SHARE = True
    GRADIO_DEBUG = True

# 导入模块化组件
from frontend.core.chat_app import ChatApp
from frontend.api.client import APIClient
from frontend.handlers.chat_handlers import (
    LLMChatHandler, RAGChatHandler, DataQAChatHandler,
    CarChatHandler, AllChatHandler, SearchHandler
)
from frontend.ui.components import UIComponents


def create_gradio_interface():
    """创建Gradio界面"""
    logger.info("开始创建Gradio界面")

    # 初始化组件
    app = ChatApp(API_BASE_URL)
    api_client = APIClient(API_BASE_URL)
    ui_components = UIComponents()

    # 初始化处理器
    llm_handler = LLMChatHandler(api_client, app)
    rag_handler = RAGChatHandler(api_client, app)
    dataqa_handler = DataQAChatHandler(api_client, app)
    car_handler = CarChatHandler(api_client, app)
    all_handler = AllChatHandler(api_client, app)
    search_handler = SearchHandler(api_client, app)

    # 包装异步生成器为同步生成器
    sync_llm_chat = ui_components.wrap_async_generator(llm_handler.chat_stream)
    sync_rag_chat = ui_components.wrap_async_generator(rag_handler.chat_stream)
    sync_dataqa_chat = ui_components.wrap_async_generator(dataqa_handler.chat_stream)
    sync_car_chat = ui_components.wrap_async_generator(car_handler.chat_stream)
    sync_all_chat = ui_components.wrap_async_generator(all_handler.chat_stream)
    sync_search = ui_components.wrap_async_generator(search_handler.search_stream)

    # 创建Gradio界面
    with gr.Blocks(
        theme=THEME,
        css=ui_components.get_full_css_and_js(),
        title=TITLE
    ) as interface:

        with gr.Row(elem_classes=["main-container"]):
            # 左侧配置区域
            config_components = ui_components.create_config_sidebar()

            # 右侧主要内容区域
            with gr.Column(elem_classes=["main-content"]):
                # 标签页
                with gr.Tabs() as tabs:
                    # LLM问答标签页
                    with gr.TabItem("💬 LLM问答", id="llm-tab"):
                        llm_components = ui_components.create_chat_interface("llm", has_reference=False)

                    # RAG问答标签页
                    with gr.TabItem("📚 硬工知识库", id="rag-tab"):
                        rag_components = ui_components.create_chat_interface("rag", has_reference=True)

                    # 汽车知识库标签页
                    with gr.TabItem("🚗 汽车知识库", id="car-tab"):
                        car_components = ui_components.create_chat_interface("car", has_reference=True)

                    # DATAQA问答标签页
                    with gr.TabItem("📊 R平台", id="dataqa-tab"):
                        dataqa_components = ui_components.create_chat_interface("dataqa", has_reference=True)

                    # 全库问答标签页
                    with gr.TabItem("🌐 全库问答", id="all-tab"):
                        all_components = ui_components.create_chat_interface("all", has_reference=True)

                    # 检索标签页
                    with gr.TabItem("🔍 检索", id="search-tab"):
                        search_components = ui_components.create_search_interface()

        # 绑定事件处理器
        _bind_event_handlers(
            config_components, llm_components, rag_components, dataqa_components,
            car_components, all_components, search_components,
            app, sync_llm_chat, sync_rag_chat, sync_dataqa_chat,
            sync_car_chat, sync_all_chat, sync_search, tabs
        )

        # 页面加载时初始化配置显示（默认显示LLM配置，因为第一个标签页是LLM问答）
        def initialize_config_display():
            """初始化配置显示"""
            print("Initializing config display - showing LLM config")
            return (
                gr.update(visible=True),   # llm_config_group
                gr.update(visible=False),  # rag_config_group  
                gr.update(visible=False)   # search_config_group
            )
        
        interface.load(
            initialize_config_display,
            outputs=[config_components["llm_config_group"],
                    config_components["rag_config_group"],
                    config_components["search_config_group"]]
        )

    logger.info("Gradio界面创建完成")
    return interface


def _bind_event_handlers(config_components, llm_components, rag_components, dataqa_components,
                        car_components, all_components, search_components,
                        app, sync_llm_chat, sync_rag_chat, sync_dataqa_chat,
                        sync_car_chat, sync_all_chat, sync_search, tabs):
    """绑定事件处理器"""

    # 创建一个隐藏的状态组件来传递当前标签页信息
    current_tab_state = gr.State(value="llm")

    # 标签页切换事件 - 动态配置显示和更新当前标签页状态
    def on_tab_select(evt: gr.SelectData):
        """处理标签页切换，动态显示配置并更新当前标签页状态"""
        tab_index = evt.index
        print(f"Tab selected: {tab_index}")  # 调试信息
        
        # 标签页映射
        tab_mapping = {0: "llm", 1: "rag", 2: "dataqa", 3: "car", 4: "allqa", 5: "search"}
        current_tab = tab_mapping.get(tab_index, "llm")
        
        if tab_index == 0:  # LLM问答 (index=0)
            # 显示LLM配置（包括问答模式、深度思考、温度、Top-p）
            print("Showing LLM config")
            return (
                gr.update(visible=True),   # llm_config_group
                gr.update(visible=False),  # rag_config_group
                gr.update(visible=False),  # search_config_group
                gr.update(visible=False),  # rag_collections (隐藏集合选择)
                current_tab                # 更新当前标签页状态
            )
        elif tab_index == 5:  # 检索 (index=5)
            # 显示检索配置（只有检索数量、重排数量、最小相似度）
            print("Showing Search config")
            return (
                gr.update(visible=False),  # llm_config_group
                gr.update(visible=False),  # rag_config_group
                gr.update(visible=True),   # search_config_group
                gr.update(visible=False),  # rag_collections (隐藏集合选择)
                current_tab                # 更新当前标签页状态
            )
        elif tab_index == 4:  # 全库问答 (index=4)
            # 显示RAG配置，包括集合选择
            print("Showing All QA config with collections")
            return (
                gr.update(visible=False),  # llm_config_group
                gr.update(visible=True),   # rag_config_group
                gr.update(visible=False),  # search_config_group
                gr.update(visible=True),   # rag_collections (显示集合选择)
                current_tab                # 更新当前标签页状态
            )
        else:
            # 硬工知识库 (index=1)、R平台问答 (index=2)、汽车知识库 (index=3)
            # 显示RAG配置（包括问答模式、深度思考、检索数量、重排数量、最小相似度）
            print("Showing RAG config")
            return (
                gr.update(visible=False),  # llm_config_group
                gr.update(visible=True),   # rag_config_group
                gr.update(visible=False),  # search_config_group
                gr.update(visible=False),  # rag_collections (隐藏集合选择)
                current_tab                # 更新当前标签页状态
            )

    # 绑定标签页切换事件
    tabs.select(
        fn=on_tab_select,
        outputs=[
            config_components["llm_config_group"],
            config_components["rag_config_group"],
            config_components["search_config_group"],
            config_components["rag_collections"],
            current_tab_state
        ]
    )

    # # 折叠按钮事件
    # def toggle_collapse():
    #     """切换侧边栏折叠状态"""
    #     # 这个函数主要是为了触发JavaScript
    #     print("Toggle collapse button clicked")  # 调试信息
    #     return gr.update()

    # config_components["collapse_btn"].click(
    #     toggle_collapse,
    #     outputs=[],
    #     js="""
    #     () => {
    #         console.log('Collapse button JS triggered');
    #         if (window.sidebarManager) {
    #             window.sidebarManager.toggleCollapse();
    #         } else {
    #             console.error('sidebarManager not found');
    #         }
    #     }
    #     """
    # )

    # 清空当前模式历史按钮事件
    def clear_current_mode_history(current_tab):
        """根据当前标签页清空对应的历史"""
        app.clear_history(current_tab)
        logger.info(f"清空{current_tab}模式历史")
        
        # 根据当前标签页返回对应的清空结果
        if current_tab == "llm":
            return ("", gr.update(), gr.update(), gr.update(), gr.update(), gr.update())
        elif current_tab == "rag":
            return (gr.update(), "", gr.update(), gr.update(), gr.update(), gr.update())
        elif current_tab == "dataqa":
            return (gr.update(), gr.update(), "", gr.update(), gr.update(), gr.update())
        elif current_tab == "car":
            return (gr.update(), gr.update(), gr.update(), "", gr.update(), gr.update())
        elif current_tab == "allqa":
            return (gr.update(), gr.update(), gr.update(), gr.update(), "", gr.update())
        elif current_tab == "search":
            return (gr.update(), gr.update(), gr.update(), gr.update(), gr.update(), "")
        else:
            return (gr.update(), gr.update(), gr.update(), gr.update(), gr.update(), gr.update())

    config_components["clear_current_btn"].click(
        clear_current_mode_history,
        inputs=[current_tab_state],
        outputs=[
            llm_components["history_output"],
            rag_components["history_output"],
            dataqa_components["history_output"],
            car_components["history_output"],
            all_components["history_output"],
            search_components["search_output"]
        ]
    )

    # LLM问答事件
    llm_components["send_btn"].click(
        sync_llm_chat,
        inputs=[
            llm_components["query_input"],
            config_components["model_id"],
            config_components["user_id"],
            config_components["llm_temperature"],
            config_components["llm_top_p"],
            config_components["llm_deep_thinking"],  # 使用LLM专用的深度思考开关
            llm_components["history_output"]
        ],
        outputs=[
            llm_components["history_output"],
            llm_components["reasoning_output"],
            llm_components["content_output"],
            config_components["first_token_time"]  # 添加首token时间输出
        ]
    )

    # RAG问答事件
    rag_components["send_btn"].click(
        sync_rag_chat,
        inputs=[
            rag_components["query_input"],
            config_components["rag_model_id"],  # 使用RAG配置中的模型ID
            config_components["user_id"],  # 使用LLM配置中的用户ID
            config_components["top_k"],
            config_components["top_r"],
            config_components["min_score"],
            config_components["rag_mode"],  # 使用RAG模式选择
            config_components["rag_deep_thinking"],  # 使用RAG专用的深度思考开关
            rag_components["history_output"]
        ],
        outputs=[
            rag_components["history_output"],
            rag_components["reference_output"],
            rag_components["reasoning_output"],
            rag_components["content_output"],
            config_components["first_token_time"]  # 添加首token时间输出
        ]
    )

    # DATAQA问答事件（R平台）
    dataqa_components["send_btn"].click(
        sync_dataqa_chat,
        inputs=[
            dataqa_components["query_input"],
            config_components["rag_model_id"],  # 使用RAG配置中的模型ID
            config_components["user_id"],  # 使用LLM配置中的用户ID
            config_components["top_k"],
            config_components["top_r"],
            config_components["min_score"],
            config_components["rag_mode"],  # 使用RAG模式选择
            config_components["rag_deep_thinking"],  # 使用RAG专用的深度思考开关
            dataqa_components["history_output"]
        ],
        outputs=[
            dataqa_components["history_output"],
            dataqa_components["reference_output"],
            dataqa_components["reasoning_output"],
            dataqa_components["content_output"],
            config_components["first_token_time"]  # 添加首token时间输出
        ]
    )

    # 汽车知识库问答事件
    car_components["send_btn"].click(
        sync_car_chat,
        inputs=[
            car_components["query_input"],
            config_components["rag_model_id"],  # 使用RAG配置中的模型ID
            config_components["user_id"],  # 使用LLM配置中的用户ID
            config_components["top_k"],
            config_components["top_r"],
            config_components["min_score"],
            config_components["rag_mode"],  # 使用RAG模式选择
            config_components["rag_deep_thinking"],  # 使用RAG专用的深度思考开关
            car_components["history_output"]
        ],
        outputs=[
            car_components["history_output"],
            car_components["reference_output"],
            car_components["reasoning_output"],
            car_components["content_output"],
            config_components["first_token_time"]  # 添加首token时间输出
        ]
    )

    # 全库问答事件
    all_components["send_btn"].click(
        sync_all_chat,
        inputs=[
            all_components["query_input"],
            config_components["rag_model_id"],  # 使用RAG配置中的模型ID
            config_components["user_id"],  # 使用LLM配置中的用户ID
            config_components["top_k"],
            config_components["top_r"],
            config_components["min_score"],
            config_components["rag_mode"],  # 使用RAG模式选择
            config_components["rag_deep_thinking"],  # 使用RAG专用的深度思考开关
            all_components["history_output"],
            config_components["rag_collections"]  # 添加集合选择参数
        ],
        outputs=[
            all_components["history_output"],
            all_components["reference_output"],
            all_components["reasoning_output"],
            all_components["content_output"],
            config_components["first_token_time"]  # 添加首token时间输出
        ]
    )

    # 检索事件 - 更新输出参数包含检索时间
    search_components["search_btn"].click(
        sync_search,
        inputs=[
            search_components["query_input"],
            config_components["user_id"],
            config_components["search_top_k"],  # 使用检索专用的top_k
            config_components["search_collections"]  # 添加集合选择参数
        ],
        outputs=[
            search_components["search_output"],
            config_components["first_token_time"]  # 添加检索时间输出
        ]
    )

    # 模型选择同步事件 - 当LLM配置中的模型改变时，同步更新RAG配置中的模型
    def sync_model_llm_to_rag(model_id):
        """从LLM配置同步模型到RAG配置"""
        return gr.update(value=model_id)

    config_components["model_id"].change(
        sync_model_llm_to_rag,
        inputs=[config_components["model_id"]],
        outputs=[config_components["rag_model_id"]]
    )

    # 模型选择同步事件 - 当RAG配置中的模型改变时，同步更新LLM配置中的模型
    def sync_model_rag_to_llm(model_id):
        """从RAG配置同步模型到LLM配置"""
        return gr.update(value=model_id)

    config_components["rag_model_id"].change(
        sync_model_rag_to_llm,
        inputs=[config_components["rag_model_id"]],
        outputs=[config_components["model_id"]]
    )


if __name__ == "__main__":
    import argparse
    
    # 添加命令行参数解析
    parser = argparse.ArgumentParser(description='启动前端应用')
    parser.add_argument('--reload', action='store_true', help='启用热重载模式')
    parser.add_argument('--host', default=GRADIO_HOST, help='服务器主机地址')
    parser.add_argument('--port', type=int, default=GRADIO_PORT, help='服务器端口')
    parser.add_argument('--share', action='store_true', default=GRADIO_SHARE, help='启用公共分享')
    parser.add_argument('--debug', action='store_true', default=GRADIO_DEBUG, help='启用调试模式')
    
    args = parser.parse_args()
    
    # 如果启用reload模式，使用gradio的reload功能
    if args.reload:
        logger.info("启动热重载模式")
        # 创建界面
        interface = create_gradio_interface()
        # 启动时启用reload
        interface.launch(
            server_name=args.host,
            server_port=args.port,
            share=args.share,
            debug=args.debug,
            reload=True,  # 启用热重载
            show_error=True,
            quiet=False
        )
    else:
        logger.info("启动正常模式")
        interface = create_gradio_interface()
        interface.launch(
            server_name=args.host,
            server_port=args.port,
            share=args.share,
            debug=args.debug
        )
