2025-07-31 22:19:09.225 | INFO     | __main__:<module>:20 - 日志初始化完成，日志路径: /Users/<USER>/work/04_dms/idp_agent/log/frontend.log
2025-07-31 22:19:09.226 | INFO     | __main__:<module>:32 - 成功导入前端配置文件
2025-07-31 22:19:09.226 | INFO     | frontend.core.chat_app:<module>:19 - 成功导入前端配置文件
2025-07-31 22:19:09.228 | INFO     | frontend.api.client:<module>:21 - 成功导入前端配置文件
警告: 环境变量 AUTH_TOKEN_GPT_4O 未设置，请设置对应的授权令牌
警告: 环境变量 AUTH_TOKEN_QWEN3_8B 未设置，请设置对应的授权令牌
警告: 环境变量 AUTH_TOKEN_QWEN3_32B 未设置，请设置对应的授权令牌
警告: 环境变量 API_ACCESS_TOKEN 未设置，使用默认令牌
2025-07-31 22:19:09.232 | INFO     | frontend.ui.components:<module>:21 - 成功导入前端配置文件
2025-07-31 22:19:09.233 | INFO     | __main__:main:278 - 启动Gradio应用...
2025-07-31 22:19:09.233 | INFO     | __main__:create_gradio_interface:59 - 开始创建Gradio界面
2025-07-31 22:19:09.233 | INFO     | frontend.core.chat_app:__init__:41 - ChatApp初始化，API地址: http://dms.ai.xiaomi.com/api/v1
2025-07-31 22:19:09.233 | INFO     | frontend.api.client:__init__:42 - APIClient初始化，API地址: http://dms.ai.xiaomi.com/api/v1
Traceback (most recent call last):
  File "/Users/<USER>/work/04_dms/idp_agent/frontend/gradio_app_v2.py", line 293, in <module>
    main()
  File "/Users/<USER>/work/04_dms/idp_agent/frontend/gradio_app_v2.py", line 279, in main
    interface = create_gradio_interface()
  File "/Users/<USER>/work/04_dms/idp_agent/frontend/gradio_app_v2.py", line 122, in create_gradio_interface
    _bind_event_handlers(
  File "/Users/<USER>/work/04_dms/idp_agent/frontend/gradio_app_v2.py", line 273, in _bind_event_handlers
    return interface
NameError: name 'interface' is not defined
/opt/miniconda3/envs/dms/lib/python3.9/site-packages/gradio/analytics.py:106: UserWarning: IMPORTANT: You are using gradio version 4.44.0, however version 4.44.1 is available, please upgrade. 
--------
  warnings.warn(
