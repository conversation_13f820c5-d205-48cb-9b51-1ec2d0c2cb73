from abc import ABC, abstractmethod
import requests
import aiohttp
import asyncio
from config.model_config import MODEL_CONFIG
import json
import openai
from typing import Dict, Any, Optional, List, AsyncGenerator
from loguru import logger
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.logging_config import configure_logging


class BaseLLMProvider(ABC):
    """LLM提供者基类"""
    def __init__(self, model_id: str, request_id: str = None):
        self.model_id = model_id
        self.config = MODEL_CONFIG[model_id]
        self.request_id = request_id
        self.service_type = self._get_service_type()
        self.logger = logger.bind(request_id=request_id)

    @abstractmethod
    def _get_service_type(self) -> str:
        """返回服务类型：'chatflow' 或 'model'"""
        pass

    @abstractmethod
    async def generate(self, query: str = "", **kwargs) -> Dict[str, Any]:
        """生成响应的抽象方法"""
        pass
    
    async def _handle_request_error(self, error: Exception) -> Dict[str, Any]:
            """统一的错误处理方法"""
            error_response = {
                "success": False,
                "error": {
                    "type": type(error).__name__,
                    "message": str(error),
                    "model_id": self.model_id
                }
            }
            # 可以添加日志记录
            return error_response

class BaseChatflowProvider(BaseLLMProvider):
    """Chatflow服务提供者基类 - 支持工作流和连续对话"""
    
    def _get_service_type(self) -> str:
        return "chatflow"
    
    async def _post_request(self, payload: dict, timeout: Optional[float] = None) -> dict:
        """发送HTTP请求到chatflow服务"""
        headers = self.config["headers"].copy()
        if self.request_id:
            headers["X-Request-ID"] = self.request_id
            # 确保inputs字典存在
            if "inputs" not in payload:
                payload["inputs"] = {}
            # 将request_id添加到inputs字典中
            payload["inputs"]["request_id"] = self.request_id
        
        try:
            # 使用传入的超时时间或配置的超时时间
            timeout_value = timeout or self.config.get("timeout", 60)
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.config["url"],
                    json=payload,
                    headers=headers,
                    timeout=timeout_value,
                ) as response:
                    response.raise_for_status()
                    return await response.json()
        except asyncio.TimeoutError as e:
            # 明确处理超时异常
            raise asyncio.TimeoutError(f"请求超时: {str(e)}, URL: {self.config['url']}")
        except Exception as e:
            raise Exception(f"请求异常: {str(e)}")
    
    async def generate(
        self,
        query: str = "",
        response_mode: str = "blocking",
        user_id: str = "abc-123",
        inputs: Optional[dict] = None,
        conversation_id: str = "",
        timeout: Optional[float] = None,
        enable_thinking: bool = False,
        **kwargs
    ) -> Dict[str, Any]:
        """Chatflow服务生成方法"""
        payload = {
            "inputs": inputs or {},
            "response_mode": response_mode,
            "user": user_id,
            "query": query,
            "conversation_id": conversation_id
        }
        if enable_thinking:
            payload["inputs"]["enable_thinking"] = True
        try:
            return await self._post_request(payload, timeout=timeout)
        except asyncio.TimeoutError as e:
            # 明确处理超时异常
            raise asyncio.TimeoutError(f"生成回复超时: {str(e)}，模型ID: {self.model_id}")
        except Exception as e:
            return await self._handle_request_error(e)

class GPT_4O_Provider(BaseChatflowProvider):
    """GPT-4O Chatflow服务提供者"""
    pass

class GPT_4O_MINI_Provider(BaseChatflowProvider):
    """GPT-4O_MINI Chatflow服务提供者"""
    pass

class BaseModelProvider(BaseLLMProvider):
    """纯模型服务提供者基类 - 直接调用模型API"""
    
    def _get_service_type(self) -> str:
        
        return "model"
    
    @abstractmethod
    async def generate(self, query: str = "", **kwargs) -> Dict[str, Any]:
        """模型服务生成方法"""
        pass

class BaseOpenAIModelProvider(BaseModelProvider):
    """OpenAI兼容的模型服务提供者基类"""
    
    def __init__(self, model_id: str, request_id: str = None):
        super().__init__(model_id, request_id)
        self.client = openai.AsyncOpenAI(
            api_key=self.config["api_key"],
            base_url=self.config["base_url"]
        )

    async def generate(self, 
                      messages: List[Dict[str, str]], 
                      temperature: Optional[float] = None,
                      top_p: Optional[float] = None, 
                      max_tokens: Optional[int] = None, 
                      timeout: Optional[float] = None,
                      conversation_id: Optional[str] = None,
                      enable_thinking: bool = False,
                      **kwargs) -> Dict[str, Any]:
        """调用OpenAI兼容的模型服务"""
        try:
            # 使用配置的超时时间或传入的超时时间
            timeout_value = timeout or self.config.get("timeout", 60)
            
            # 设置请求超时
            self.client.timeout = aiohttp.ClientTimeout(total=timeout_value)
            
            response = await self.client.chat.completions.create(
                model=self.config["model"],
                messages=messages,
                temperature=temperature or self.config.get("temperature", 0.7),
                top_p=top_p or self.config.get("top_p", 1.0),
                max_tokens=max_tokens or self.config.get("max_tokens", 2000),
                timeout=timeout_value,
                enable_thinking=enable_thinking,
            )
            # 将ChatCompletion对象转换为字典格式
            return {
                "content": response.choices[0].message.content,
                "model": response.model,
                "id": response.id,
                "created": response.created,
                "usage": response.usage.model_dump() if response.usage else None
            }
        except asyncio.TimeoutError as e:
            # 明确处理超时异常
            raise asyncio.TimeoutError(f"生成回复超时: {str(e)}，模型ID: {self.model_id}")
        except Exception as e:
            return await self._handle_request_error(e)

    async def generate_stream(
            self, 
            messages: List[Dict[str, str]], 
            temperature: Optional[float] = None,
            top_p: Optional[float] = None, 
            max_tokens: Optional[int] = None,
            timeout: Optional[float] = None,
            conversation_id: Optional[str] = None,
            enable_thinking: bool = True,
            **kwargs
        ) -> AsyncGenerator[Dict[str, Any], None]:
            """流式对话生成方法
            
            Args:
                messages: 对话消息列表
                temperature: 温度参数
                top_p: top-p采样参数
                max_tokens: 最大token数
                timeout: 超时时间（秒）
                
            Yields:
                Dict[str, Any]: 包含响应内容的字典，格式为:
                    {'type': 'content|reasoning', 'content': str}
            """
            try:
                # 使用配置的超时时间或传入的超时时间
                timeout_value = timeout or self.config.get("timeout", 60)
                # 设置请求超时
                self.client.timeout = aiohttp.ClientTimeout(total=timeout_value)
                extra_body = {}
                self.logger.info(f"enable_thinking: {enable_thinking}")
                if enable_thinking:
                    print(f"开启思考模式")
                    extra_body["enable_reasoning"] = True
                    # extra_body["thinking_budget"] = 4096
                else:
                # # 添加一个系统消息，指示模型直接回答，不要思考
                #     extra_body["enable_reasoning"] = False
                #     print(f"添加一个系统消息，指示模型直接回答，不要思考")
                #     if messages and messages[0].get("role") == "system":
                #         original_system = messages[0]["content"]
                #         messages[0]["content"] = f"{original_system} 请直接回答问题，不要展示思考过程。"
                #     else:
                #         messages.insert(0, {"role": "system", "content": "请直接回答问题，不要展示思考过程。"})
                #     print(f"messages: {messages}")
                    extra_body["enable_reasoning"] = True
                    if messages and messages[-1].get("role") == "user":
                        original_user = messages[-1]["content"]
                        messages[-1]["content"] = f"{original_user} /no_think"
                
                # print(f"messages: {messages}")
                    
                stream = await self.client.chat.completions.create(
                    model=self.config["model"],
                    messages=messages,
                    temperature=temperature or self.config.get("temperature", 0.7),
                    top_p=top_p or self.config.get("top_p", 1.0),
                    max_tokens=max_tokens or self.config.get("max_tokens", 2000),
                    stream=True,
                    timeout=timeout_value,
                    extra_body=extra_body if extra_body else None
                )
                async for chunk in stream:
                    # print(f"chunk: {chunk}")
                    if chunk.choices[0].delta.content is not None:
                        yield {
                            'type': 'content',
                            'content': chunk.choices[0].delta.content,
                            'role': "" if not chunk.choices[0].delta.role else chunk.choices[0].delta.role,
                            'finish_reason': "" if not chunk.choices[0].finish_reason else chunk.choices[0].finish_reason
                        }
                    if hasattr(chunk.choices[0].delta, 'reasoning_content') and chunk.choices[0].delta.reasoning_content is not None and enable_thinking:
                        yield {
                            'type': 'reasoning',
                            'content': chunk.choices[0].delta.reasoning_content,
                            'role': "" if not chunk.choices[0].delta.role else chunk.choices[0].delta.role,
                            'finish_reason': "" if not chunk.choices[0].finish_reason else chunk.choices[0].finish_reason
                        }
            except asyncio.TimeoutError as e:
                # 明确处理超时异常
                self.logger.info(f"流式生成超时: {str(e)}，模型ID: {self.model_id}")
                yield {"type": "error", "content": f"流式生成超时: {str(e)}，模型ID: {self.model_id}"}
                raise asyncio.TimeoutError(f"流式生成超时: {str(e)}，模型ID: {self.model_id}")
            except Exception as e:
                self.logger.info(f"流式生成异常: {str(e)}，模型ID: {self.model_id}")
                yield {"type": "error", "content": str(e)}

class QWEN3_32B_Provider(BaseOpenAIModelProvider):
    """Qwen3-32B模型服务提供者"""
    pass

class QWEN3_8B_Provider(BaseOpenAIModelProvider):
    """Qwen3-8B模型服务提供者"""
    pass

class BasePureThinkingProvider(BaseOpenAIModelProvider):
    """纯思考模型提供者基类 - 总是输出reasoning_content"""

    async def generate_stream(
            self,
            messages: List[Dict[str, str]],
            temperature: Optional[float] = None,
            top_p: Optional[float] = None,
            max_tokens: Optional[int] = None,
            timeout: Optional[float] = None,
            conversation_id: Optional[str] = None,
            enable_thinking: bool = True,
            **kwargs
        ) -> AsyncGenerator[Dict[str, Any], None]:
            """纯思考模型的流式对话生成方法

            纯思考模型总是会输出reasoning_content，不需要额外参数控制
            """
            try:
                # 使用配置的超时时间或传入的超时时间
                timeout_value = timeout or self.config.get("timeout", 60)
                # 设置请求超时
                self.client.timeout = aiohttp.ClientTimeout(total=timeout_value)

                # 纯思考模型不需要额外的参数，直接调用
                stream = await self.client.chat.completions.create(
                    model=self.config["model"],
                    messages=messages,
                    temperature=temperature or self.config.get("temperature", 0.5),
                    top_p=top_p or self.config.get("top_p", 0.5),
                    max_tokens=max_tokens or self.config.get("max_tokens", 2048),
                    stream=True,
                    timeout=timeout_value
                )

                async for chunk in stream:
                    # 处理content内容
                    if chunk.choices[0].delta.content is not None:
                        yield {
                            'type': 'content',
                            'content': chunk.choices[0].delta.content,
                            'role': "" if not chunk.choices[0].delta.role else chunk.choices[0].delta.role,
                            'finish_reason': "" if not chunk.choices[0].finish_reason else chunk.choices[0].finish_reason
                        }
                    # 处理reasoning_content内容（纯思考模型总是有这个字段）
                    if hasattr(chunk.choices[0].delta, 'reasoning_content') and chunk.choices[0].delta.reasoning_content is not None:
                        yield {
                            'type': 'reasoning',
                            'content': chunk.choices[0].delta.reasoning_content,
                            'role': "" if not chunk.choices[0].delta.role else chunk.choices[0].delta.role,
                            'finish_reason': "" if not chunk.choices[0].finish_reason else chunk.choices[0].finish_reason
                        }
            except asyncio.TimeoutError as e:
                self.logger.info(f"流式生成超时: {str(e)}，模型ID: {self.model_id}")
                yield {"type": "error", "content": f"流式生成超时: {str(e)}，模型ID: {self.model_id}"}
                raise asyncio.TimeoutError(f"流式生成超时: {str(e)}，模型ID: {self.model_id}")
            except Exception as e:
                self.logger.info(f"流式生成异常: {str(e)}，模型ID: {self.model_id}")
                yield {"type": "error", "content": str(e)}

class BaseNonThinkingProvider(BaseOpenAIModelProvider):
    """非思考模型提供者基类 - 不输出reasoning_content"""

    async def generate_stream(
            self,
            messages: List[Dict[str, str]],
            temperature: Optional[float] = None,
            top_p: Optional[float] = None,
            max_tokens: Optional[int] = None,
            timeout: Optional[float] = None,
            conversation_id: Optional[str] = None,
            enable_thinking: bool = True,
            **kwargs
        ) -> AsyncGenerator[Dict[str, Any], None]:
            """非思考模型的流式对话生成方法

            非思考模型不会输出reasoning_content字段
            """
            try:
                # 使用配置的超时时间或传入的超时时间
                timeout_value = timeout or self.config.get("timeout", 60)
                # 设置请求超时
                self.client.timeout = aiohttp.ClientTimeout(total=timeout_value)

                # 非思考模型不需要额外的参数，直接调用
                stream = await self.client.chat.completions.create(
                    model=self.config["model"],
                    messages=messages,
                    temperature=temperature or self.config.get("temperature", 0.5),
                    top_p=top_p or self.config.get("top_p", 0.5),
                    max_tokens=max_tokens or self.config.get("max_tokens", 2048),
                    stream=True,
                    timeout=timeout_value
                )

                async for chunk in stream:
                    # 只处理content内容，非思考模型不会有reasoning_content
                    if chunk.choices[0].delta.content is not None:
                        yield {
                            'type': 'content',
                            'content': chunk.choices[0].delta.content,
                            'role': "" if not chunk.choices[0].delta.role else chunk.choices[0].delta.role,
                            'finish_reason': "" if not chunk.choices[0].finish_reason else chunk.choices[0].finish_reason
                        }
            except asyncio.TimeoutError as e:
                self.logger.info(f"流式生成超时: {str(e)}，模型ID: {self.model_id}")
                yield {"type": "error", "content": f"流式生成超时: {str(e)}，模型ID: {self.model_id}"}
                raise asyncio.TimeoutError(f"流式生成超时: {str(e)}，模型ID: {self.model_id}")
            except Exception as e:
                self.logger.info(f"流式生成异常: {str(e)}，模型ID: {self.model_id}")
                yield {"type": "error", "content": str(e)}

class QWEN3_235B_Thinking_Provider(BasePureThinkingProvider):
    """Qwen3-235B-A22B-Thinking-2507思考模型服务提供者"""
    pass

class QWEN3_235B_Instruct_Provider(BaseNonThinkingProvider):
    """Qwen3-235B-A22B-Instruct-2507非思考模型服务提供者"""
    pass

# 服务类型映射
SERVICE_TYPE_MAPPING = {
    "gpt_4o": "chatflow",
    "gpt_4o_mini": "chatflow",
    "qwen3_32b": "model",
    "qwen3_8b": "model",
    "qwen3_235b_thinking": "model",
    "qwen3_235b_instruct": "model"
}

# Provider类映射
PROVIDER_MAPPING = {
    "gpt_4o": GPT_4O_Provider,
    "gpt_4o_mini": GPT_4O_MINI_Provider,
    "qwen3_32b": QWEN3_32B_Provider,
    "qwen3_8b": QWEN3_8B_Provider,
    "qwen3_235b_thinking": QWEN3_235B_Thinking_Provider,
    "qwen3_235b_instruct": QWEN3_235B_Instruct_Provider
    }

def get_llm_provider(model_id: str, request_id: str = None, enable_thinking: bool = True) -> BaseLLMProvider:
    """获取LLM提供者实例

    Args:
        model_id: 模型ID，支持qwen3_235b_2507动态选择
        request_id: 请求ID
        enable_thinking: 是否启用思考（仅对qwen3_235b_2507有效）

    Returns:
        BaseLLMProvider: 对应的提供者实例
    """
    # 特殊处理qwen3_235b_2507，根据enable_thinking选择具体模型
    if model_id == "qwen3_235b_2507":
        from config.model_config import resolve_235b_model
        actual_model_id = resolve_235b_model(enable_thinking)
    else:
        actual_model_id = model_id

    if actual_model_id not in PROVIDER_MAPPING:
        supported_models = list(PROVIDER_MAPPING.keys()) + ["qwen3_235b_2507"]
        raise ValueError(f"不支持的模型ID: {model_id}. 支持的模型: {supported_models}")

    provider_class = PROVIDER_MAPPING[actual_model_id]
    return provider_class(actual_model_id, request_id)

def get_chatflow_providers() -> Dict[str, type]:
    """获取所有chatflow类型的提供者"""
    return {
        model_id: provider_class 
        for model_id, provider_class in PROVIDER_MAPPING.items()
        if SERVICE_TYPE_MAPPING.get(model_id) == "chatflow"
    }

def get_model_providers() -> Dict[str, type]:
    """获取所有model类型的提供者"""
    return {
        model_id: provider_class 
        for model_id, provider_class in PROVIDER_MAPPING.items()
        if SERVICE_TYPE_MAPPING.get(model_id) == "model"
    }

def is_chatflow_service(model_id: str) -> bool:
    """判断是否为chatflow服务"""
    return SERVICE_TYPE_MAPPING.get(model_id) == "chatflow"

def is_model_service(model_id: str) -> bool:
    """判断是否为model服务"""
    return SERVICE_TYPE_MAPPING.get(model_id) == "model"
